# Power Plant Intelligence System

## Repository Summary
This project is an advanced Power Plant Data Intelligence System that extracts comprehensive power plant information from web research, generating structured JSON data that complies with specific templates for organization-level, plant-level, and unit-level information. It consists of a React frontend and a LangGraph-powered backend.

## Repository Structure
- **frontend/**: React application with Vite
- **backend/**: LangGraph/FastAPI application
- **docker-compose.yml**: Production deployment configuration
- **Dockerfile**: Multi-stage build for containerization
- **Makefile**: Development workflow commands
- **requirements.txt**: Python dependencies
- **org_level.json/plant_level.json**: JSON templates for data structure

## Projects

### Backend (LangGraph Agent)
**Configuration File**: `backend/pyproject.toml`

#### Language & Runtime
**Language**: Python
**Version**: >=3.11,<4.0
**Build System**: setuptools
**Package Manager**: pip

#### Dependencies
**Main Dependencies**:
- langgraph (>=0.2.6)
- langchain (>=0.3.19)
- langchain-google-genai
- google-genai
- fastapi
- boto3 (AWS S3 integration)
- beautifulsoup4, opencv-python, easyocr (image processing)

**Development Dependencies**:
- mypy (>=1.11.1)
- ruff (>=0.6.1)
- pytest (>=8.3.5)

#### Build & Installation
```bash
cd backend
pip install .
```

#### Docker
**Dockerfile**: Uses `langchain/langgraph-api:3.11` as base image
**Dependencies**: Redis for pub-sub, PostgreSQL for state persistence
**Run Command**:
```bash
GEMINI_API_KEY=<key> LANGSMITH_API_KEY=<key> docker-compose up
```

#### Testing
**Framework**: pytest
**Test Files**: `test_multiple_plants.py`, `test_s3_folder_structure.py`
**Run Command**:
```bash
pytest
```

### Frontend (React Application)
**Configuration File**: `frontend/package.json`

#### Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: Node.js 20 (based on Dockerfile)
**Build System**: Vite
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- react (^19.0.0)
- react-dom (^19.0.0)
- @langchain/langgraph-sdk (^0.0.74)
- @langchain/core (^0.3.55)
- tailwindcss (^4.1.5)
- react-markdown (^9.0.3)
- react-router-dom (^7.5.3)

**Development Dependencies**:
- typescript (~5.7.2)
- vite (^6.3.4)
- eslint (^9.22.0)
- @vitejs/plugin-react-swc (^3.9.0)

#### Build & Installation
```bash
cd frontend
npm install
npm run build
```

#### Development
**Run Command**:
```bash
make dev-frontend  # Frontend only
make dev           # Both frontend and backend
```

## Usage & Operations
The system uses Google Gemini models for power plant data extraction, with a two-phase approach:
1. Organization-level data collection
2. Plant-level data collection with multi-plant detection

The extracted data is stored in AWS S3 with proper metadata, following predefined JSON templates.

## External Services
- **Google Gemini API**: LLM for specialized power plant data extraction
- **Google Custom Search API**: Web research capabilities
- **AWS S3**: Structured JSON storage and image hosting
- **PostgreSQL**: State persistence and thread management
- **Redis**: Real-time streaming and pub-sub messaging