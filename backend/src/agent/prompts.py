from datetime import datetime


# Get current date in a readable format
def get_current_date():
    return datetime.now().strftime("%B %d, %Y")


query_writer_instructions = """Your goal is to generate sophisticated search queries to gather comprehensive information about a power plant. The user has provided only the name of a power plant, and you need to create queries that will help extract specific details about it.

Instructions:
- Generate search queries to find detailed information about the power plant named in the research topic.
- Focus on queries that will help extract the following specific information:

ORGANIZATION LEVEL INFORMATION:
  1. The ownership type (public, private, or joint-venture)
  2. The country and province/state where it's located
  3. The parent organization/company that owns or operates it
  4. The currency used in that country and its financial year reporting period
  5. The number of power plant sites (or projects) owned by the power plant (must be a precise number. Also this is not the same as number of generating units of a power plant)
  6. The types of power generation technologies used
  7. Information about Power Purchase Agreements (PPAs)

PLANT LEVEL DETAILED INFORMATION:
  8. The plant's official name and unique identifier
  9. The plant's specific technology or fuel type
  10. The plant's precise location (latitude, longitude, and address)
  11. List of operational units at the plant
  12. Detailed PPA information including:
     - Capacity and unit
     - Start and end dates
     - Contract duration
     - Counterparties/respondents and their details
  13. Grid connectivity information including:
     - Substation names and types
     - Connection capacities
     - Substation locations
     - Connected projects and distances

- Consider searching on Gemwiki and other specialized energy databases for detailed information
- Include queries that might reveal grid connectivity information through maps or technical documents
- Don't produce more than {number_queries} queries.
- Ensure queries are diverse and cover different aspects of the required information.
- The current date is {current_date}, so focus on getting the most up-to-date information.

Format: 
- Format your response as a JSON object with ALL three of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant for gathering power plant information
   - "query": A list of search queries

Example:

Topic: Adani Power Plant
```json
{{
    "rationale": "To gather comprehensive information about Adani Power Plant, we need to search for its ownership structure, location details, parent company information, financial details, and technical specifications. These queries target different aspects of the required information.",
    "query": ["Adani Power Plant ownership structure public private joint venture", "Adani Power Plant location country province", "Adani Power parent company total number of power plants", "Adani Power Plant generation technology types", "Adani Power Plant PPA structure unit level or plant level", "Adani Power Plant exact coordinates latitude longitude", "Adani Power Plant grid connectivity substation details", "Adani Power Plant PPA details capacity start date end date", "Adani Power Plant operational units list", "Gemwiki Adani Power Plant technical specifications"],
}}
```

Context: {research_topic}"""


web_searcher_instructions = """Conduct targeted Google Searches to gather comprehensive information about the power plant mentioned in the research topic. You need to extract specific details about this power plant.

Instructions:
- The current date is {current_date}, so focus on the most recent information.
- Search for and extract the following specific details about the power plant:

ORGANIZATION LEVEL INFORMATION:
  1. CFPP Type: Whether it's publicly owned, privately owned, or a joint venture
  2. Country Name: The full name of the country where it's located
  3. Currency: The ISO currency code used in that country (e.g., USD, EUR, INR)
  4. Financial Year: The fiscal year reporting period used in that country (in MM-MM format)
  5. Organization Name: The full legal name of the company/entity that owns/operates it
  6. Plants Count: The number of power plant sites (or projects) owned by the power plant (must be a precise number. Also this is not the same as number of generating units of a power plant)
  7. Plant Types: All types of power generation technologies used (coal, gas, hydro, etc.)
  8. PPA Flag: Whether Power Purchase Agreements are at unit-level or plant-level
  9. Province: The state/province/region where the plant is located

PLANT LEVEL DETAILED INFORMATION:
  10. Plant Name: The official name of the specific power plant
  11. Plant ID: A unique identifier for this plant (Start from 1 and go till the number of power plant sites)
  12. Plant Type: The specific technology or fuel type used at this plant
  13. Location: Precise latitude and longitude coordinates of the plant
  14. Plant Address: Detailed address including district/city, state, country
  15. Units: List of operational units at this plant
  16. PPA Details: For each PPA, collect:
      - Capacity and its unit (e.g., 500 MW)
      - Start and end dates (in ISO format YYYY-MM-DD)
      - Tenure and tenure type (e.g., 25 Years)
      - Respondent details (counterparties, their capacities, prices, etc.)
  17. Grid Connectivity: For each grid connection, collect:
      - Substation name and type
      - Connection capacity
      - Substation coordinates (latitude/longitude)
      - Connected projects and their distances from the substation

- Check specialized sources like Gemwiki and energy databases for detailed information
- Look for technical documents, regulatory filings, and company reports for PPA details
- Search for grid connectivity maps or transmission planning documents
- Be precise and factual. Only include information that you can verify from credible sources.
- Track all sources meticulously for citation purposes.
- Focus on extracting the specific data points listed above rather than general information.

Research Topic:
{research_topic}
"""

reflection_instructions = """You are an expert analyst evaluating information gathered about a power plant. Your task is to determine if we have sufficient information about all required data points.

Instructions:
- Review the summaries to determine if we have gathered all the required information about the power plant.
- The required information includes:

ORGANIZATION LEVEL INFORMATION:
  1. CFPP Type (public/private/joint-venture)
  2. Country Name (full country name)
  3. Currency (ISO code)
  4. Financial Year (MM-MM format)
  5. Organization Name (full legal name)
  6. Plants Count (exact number owned by the power plant, must be a precise number)
  7. Plant Types (list of generation technologies)
  8. PPA Flag (plant-level or unit-level)
  9. Province (state/region)

PLANT LEVEL DETAILED INFORMATION:
  10. Plant Name (official name)
  11. Plant ID (unique identifier)
  12. Plant Type (specific technology/fuel)
  13. Location (latitude and longitude)
  14. Plant Address (district/city, state, country)
  15. Units (list of operational units)
  16. PPA Details:
      - Capacity and unit
      - Start and end dates
      - Tenure and tenure type
      - Respondent details
  17. Grid Connectivity:
      - Substation information
      - Connection capacity
      - Substation coordinates
      - Connected projects and distances

- Identify any missing or unclear information and generate targeted follow-up queries.
- If all information is available and clear, indicate that the research is sufficient.
- Prioritize getting complete organization-level information first, then plant-level details.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe which specific data points are missing or unclear
   - "follow_up_queries": Generate specific queries to find the missing information

Example:
```json
{{
    "is_sufficient": false,
    "knowledge_gap": "Missing information about the financial year reporting period and PPA structure. The plant types are also not comprehensively listed. For plant-level details, we're missing grid connectivity information and complete PPA details including start/end dates and respondent information.",
    "follow_up_queries": ["Financial year reporting period for [Country] where [Power Plant] is located", "[Power Plant] power purchase agreement structure plant level or unit level", "[Power Plant] complete list of generation technologies", "[Power Plant] grid connectivity substation details", "[Power Plant] PPA details start date end date respondents"]
}}
```

Reflect carefully on the Summaries to identify which specific data points are missing, then produce your output following this JSON format:

Summaries:
{summaries}
"""

# Unit-level instructions for comprehensive unit processing
unit_query_writer_instructions = """Your goal is to generate sophisticated search queries to gather comprehensive UNIT-LEVEL technical information about a specific unit of a power plant.

Instructions:
- Generate search queries to find detailed technical information about Unit {unit_number} of the power plant named in the research topic.
- Focus EXCLUSIVELY on queries that will help extract the following UNIT-LEVEL technical information:

CRITICAL UNIT DATA:
  1. Unit specifications: capacity (MW), technology type, unit_number, boiler_type, commencement_date
  2. Operational performance: PLF (Plant Load Factor) by year, PAF (Plant Availability Factor) by year, unit_efficiency (%), auxiliary_power_consumed by year
  3. Technical efficiency: heat_rate and heat_rate_unit (kJ/kWh or kcal/kWh), station heat rate for the specific technology
  4. Power generation: gross_power_generation by year, emission_factor (kg CO2e/kWh) by year
  5. Fuel specifications: fuel_type details, selected_coal_type, selected_biomass_type, fuel percentages by year
  6. Country-specific fuel data: gcv_coal (kCal/kg), gcv_natural_gas (MJ/m³), gcv_biomass (kCal/kg) for the plant's country
  7. Technology-specific efficiency: For the plant's country - CCGT efficiency, OCGT efficiency, combined_cycle_heat_rate, open_cycle_heat_rate
  8. Conversion economics: capex_required_retrofit (biomass cofiring), capex_required_renovation_open_cycle, capex_required_renovation_closed_cycle, efficiency_loss_cofiring
  9. Lifecycle information: unit_lifetime (years), remaining_useful_life (end date)

SEARCH STRATEGY:
- Include the specific unit number (Unit {unit_number}) in your queries
- Search for technical specifications, performance reports, and operational data
- Look for country-specific fuel properties and efficiency standards
- Search for technology-specific efficiency data for the plant's country
- Include queries for conversion/retrofit cost estimates
- Search for detailed technical reports and regulatory filings

- Don't produce more than {number_queries} queries.
- Ensure queries are diverse and cover different aspects of unit-level technical information.
- The current date is {current_date}, so focus on getting the most up-to-date technical data.

Format: 
- Format your response as a JSON object with ALL three of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant for gathering unit-level technical information
   - "query": A list of search queries

Research Topic: {research_topic}
Unit Number: {unit_number}
"""

unit_web_searcher_instructions = """Conduct targeted Google Searches to gather comprehensive unit-level technical information about Unit {unit_number} of the power plant mentioned in the research topic.

Instructions:
- The current date is {current_date}, so focus on the most recent information.
- Search for and extract the following specific details about Unit {unit_number}:

UNIT TECHNICAL SPECIFICATIONS:
  1. Unit Number: The specific unit identifier (Unit {unit_number})
  2. Capacity: Unit-wise installed capacity in MW
  3. Technology: Specific technology type (Ultra Super Critical, Super Critical, etc.)
  4. Boiler Type: Type of boiler used in this unit
  5. Commencement Date: Commercial operation date (yyyy-mm-ddThh:mm:ss.msZ format)
  6. Unit Lifetime: Total operational lifetime in years
  7. Remaining Useful Life: Expected end-of-life date

PERFORMANCE METRICS (Historical Data by Year):
  8. PLF (Plant Load Factor): Yearly capacity utilization percentage
  9. PAF (Plant Availability Factor): Yearly availability percentage
  10. Auxiliary Power Consumed: Yearly internal power consumption percentage
  11. Gross Power Generation: Total energy generated yearly
  12. Emission Factor: CO2 emissions per kWh yearly (kg CO2e/kWh)

EFFICIENCY DATA:
  13. Unit Efficiency: Overall unit efficiency percentage
  14. Heat Rate: Station heat rate (kJ/kWh or kcal/kWh)
  15. Heat Rate Unit: Measurement unit for heat rate

FUEL INFORMATION:
  16. Fuel Type: Primary fuel types used with yearly percentages
  17. Selected Coal Type: Specific coal type used
  18. Selected Biomass Type: Biomass type for cofiring (if applicable)

COUNTRY-SPECIFIC PARAMETERS:
  19. GCV Coal: Gross calorific value of coal in the plant's country (kCal/kg)
  20. GCV Natural Gas: Gross calorific value of natural gas in the country
  21. GCV Biomass: Gross calorific value of biomass in the country
  22. OCGT Efficiency: Open cycle gas turbine efficiency for the country
  23. CCGT Efficiency: Combined cycle gas turbine efficiency for the country
  24. Combined Cycle Heat Rate: Average heat rate for CCGT plants in the country
  25. Open Cycle Heat Rate: Average heat rate for OCGT plants in the country

CONVERSION ECONOMICS:
  26. CAPEX Required Retrofit: Cost for biomass cofiring retrofit
  27. CAPEX Required Renovation Open Cycle: Cost for open cycle gas conversion
  28. CAPEX Required Renovation Closed Cycle: Cost for closed cycle gas conversion
  29. Efficiency Loss Cofiring: Efficiency reduction from biomass cofiring

UNIT-LEVEL PPA DETAILS:
  30. Unit-specific PPA information including capacity, dates, tenure, respondents

- Check specialized sources like technical reports, regulatory filings, and industry databases
- Look for unit-specific performance data from annual reports or grid operator data
- Search for country-specific fuel and technology parameters from national energy agencies
- Be precise and factual. Only include information that you can verify from credible sources
- Track all sources meticulously for citation purposes
- Focus on Unit {unit_number} specifically, not general plant-level information

Research Topic: {research_topic}
Unit Number: {unit_number}
"""

unit_reflection_instructions = """You are an expert analyst evaluating unit-level technical information gathered about Unit {unit_number} of a power plant. Your task is to determine if we have sufficient detailed technical data.

Instructions:
- Review the summaries to determine if we have gathered all the required unit-level technical information.
- The required information includes:

UNIT TECHNICAL SPECIFICATIONS:
  1. Unit Number, Capacity (MW), Technology Type, Boiler Type
  2. Commencement Date, Unit Lifetime, Remaining Useful Life

PERFORMANCE METRICS (Historical Data):
  3. PLF (Plant Load Factor) by year
  4. PAF (Plant Availability Factor) by year  
  5. Auxiliary Power Consumed by year
  6. Gross Power Generation by year
  7. Emission Factor by year

EFFICIENCY DATA:
  8. Unit Efficiency (%), Heat Rate, Heat Rate Unit

FUEL INFORMATION:
  9. Fuel Type details with yearly percentages
  10. Selected Coal Type, Selected Biomass Type

COUNTRY-SPECIFIC PARAMETERS:
  11. GCV values for coal, natural gas, biomass
  12. OCGT and CCGT efficiency for the country
  13. Heat rates for gas turbine technologies

CONVERSION ECONOMICS:
  14. CAPEX requirements for various conversions
  15. Efficiency loss from cofiring

UNIT-LEVEL PPA DETAILS:
  16. Unit-specific PPA information

- Identify any missing or unclear technical information and generate targeted follow-up queries
- If all critical technical information is available and clear, indicate that the research is sufficient
- Prioritize getting complete technical specifications first, then performance data, then economic data

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe which specific technical data points are missing or unclear
   - "follow_up_queries": Generate specific queries to find the missing technical information

Example:
```json
{{
    "is_sufficient": false,
    "knowledge_gap": "Missing critical performance data including PLF and PAF by year for Unit {unit_number}. Also missing country-specific fuel GCV values and heat rate data. Unit efficiency and heat rate information is incomplete.",
    "follow_up_queries": ["Unit {unit_number} PLF Plant Load Factor historical data by year", "Unit {unit_number} PAF Plant Availability Factor performance data", "GCV gross calorific value coal natural gas biomass [Country]", "[Power Plant] Unit {unit_number} heat rate efficiency technical specifications"]
}}
```

Reflect carefully on the Summaries to identify which specific technical data points are missing, then produce your output following this JSON format:

Unit Number: {unit_number}
Summaries:
{summaries}
"""

unit_answer_instructions = """Extract and format comprehensive unit-level technical information about Unit {unit_number} from the research summaries into a structured JSON format.

Instructions:
- The current date is {current_date}.
- Extract the following specific technical information about Unit {unit_number} from the provided summaries:

UNIT IDENTIFICATION:
  1. sk: Create unique identifier in format "scraped#unit#{plant_type}#{unit_number}#{plant_name}#{plant_id}"
  2. unit_number: The unit number identifier
  3. plant_id: Unique identifier for the plant

TECHNICAL SPECIFICATIONS:
  4. capacity: Unit-wise installed capacity in MW
  5. capacity_unit: Unit for capacity (typically 'MW')
  6. technology: Technology classification (Ultra Super Critical, Super Critical, etc.)
  7. boiler_type: Type of boiler used
  8. commencement_date: Commercial operation date (yyyy-mm-ddThh:mm:ss.msZ)
  9. remaining_useful_life: End-of-life date (yyyy-mm-ddThh:mm:ss.msZ)
  10. unit_lifetime: Total operational lifetime in years

PERFORMANCE METRICS (Arrays with yearly data):
  11. plf: Array of objects with "value" and "year" for Plant Load Factor
  12. PAF: Array of objects with "value" and "year" for Plant Availability Factor
  13. auxiliary_power_consumed: Array of yearly auxiliary power consumption data
  14. gross_power_generation: Array of yearly power generation data
  15. emission_factor: Array of yearly emission factor data (kg CO2e/kWh)

EFFICIENCY METRICS:
  16. unit_efficiency: Unit-specific efficiency percentage
  17. unit: Unit efficiency measurement unit (%)
  18. heat_rate: Station heat rate value
  19. heat_rate_unit: Heat rate measurement unit

FUEL INFORMATION:
  20. fuel_type: Array of fuel objects with fuel, type, and years_percentage
  21. selected_coal_type: Primary coal type used
  22. selected_biomass_type: Biomass type for cofiring

COUNTRY-SPECIFIC PARAMETERS:
  23. gcv_coal: Gross calorific value of coal (kCal/kg)
  24. gcv_coal_unit: Unit for coal GCV
  25. gcv_natural_gas: Gross calorific value of natural gas
  26. gcv_natural_gas_unit: Unit for gas GCV
  27. gcv_biomass: Gross calorific value of biomass
  28. gcv_biomass_unit: Unit for biomass GCV
  29. open_cycle_gas_turbine_efficency: OCGT efficiency for the country
  30. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country
  31. combined_cycle_heat_rate: CCGT heat rate for the country
  32. open_cycle_heat_rate: OCGT heat rate for the country

CONVERSION ECONOMICS:
  33. capex_required_retrofit: CAPEX for biomass cofiring retrofit
  34. capex_required_retrofit_unit: Unit for retrofit CAPEX
  35. capex_required_renovation_open_cycle: CAPEX for open cycle conversion
  36. capex_required_renovation_open_cycle_unit: Unit for open cycle CAPEX
  37. capex_required_renovation_closed_cycle: CAPEX for closed cycle conversion
  38. capex_required_renovation_closed_cycle_unit: Unit for closed cycle CAPEX
  39. efficiency_loss_cofiring: Efficiency reduction from cofiring

UNIT PPA DETAILS:
  40. ppa_details: Array of unit-specific PPA objects

- Format your response ONLY as a clean JSON object with these exact field names
- Do not include any explanatory text before or after the JSON
- If any information is not available, use "Not available" for strings, 0 for numbers, or empty arrays for lists
- Ensure all arrays follow the exact structure with "value" and "year" fields for time-series data
- For fuel_type, use the structure: {{"fuel": "Coal", "type": "Bituminous", "years_percentage": {{"2023": "80", "2022": "85"}}}}

Example format for time-series data:
```json
"plf": [
  {{"value": "75.5", "year": "2023"}},
  {{"value": "78.2", "year": "2022"}}
]
```

User Context:
- {research_topic}
- Unit Number: {unit_number}

Extract unit-level technical data from the following summaries:
{summaries}
"""

answer_instructions = """Extract and format specific information about the power plant from the research summaries into a structured JSON format.

Instructions:
- The current date is {current_date}.
- Extract the following specific information about the power plant from the provided summaries:

ORGANIZATION LEVEL INFORMATION:
  1. cfpp_type: Classification as "Public", "Private", or "Joint-Venture"
  2. country_name: Full name of the country where the plant is located
  3. currency_in: ISO currency code of the country (e.g., USD, EUR, INR)
  4. financial_year: Fiscal year format in MM-MM (e.g., "04-03" for April-March)
  5. organization_name: Full legal name of the owning/operating company
  6. plants_count: Exact number of power plants owned by the power plant (must be a precise number, not a vague term like "Multiple". Also it's not the number of generating units)
  7. plant_types: List of all power generation technologies used
  8. ppa_flag: Whether PPAs are at "Plant-level" or "Unit-level"
  9. province: State/province/region where the plant is located

PLANT LEVEL DETAILED INFORMATION:
  10. name: The official name of the power plant
  11. plant_id: A unique identifier assigned to this plant (integer)
  12. plant_type: The technology or fuel type of the plant site
  13. lat: The plant's latitude coordinate
  14. long: The plant's longitude coordinate
  15. plant_address: District or city, State, Country
  16. units_id: List of units which are operational at this plant
  17. ppa_details: Array of PPA objects, each containing:
      - capacity: The capacity covered by this PPA
      - capacity_unit: The unit of that capacity (e.g., 'MW', 'kW')
      - start_date: The PPA's commencement date (ISO format, YYYY-MM-DD)
      - end_date: The PPA's termination date (ISO format, YYYY-MM-DD)
      - tenure: The numeric duration of the PPA
      - tenure_type: The unit for the tenure (e.g., 'Years', 'Months')
      - respondents: Array of respondent objects, each containing:
        * name: The counterparty's name
        * capacity: The capacity volume contracted by this respondent
        * currency: The currency in which the price is denominated
        * price: The contracted price per unit of energy or capacity
        * price_unit: The basis for the price (e.g., '$/MWh', 'INR/kW-year')
  18. grid_connectivity_maps: Array of grid connectivity objects, each containing:
      - details: Array of detail objects, each containing:
        * substation_name: The official name of the substation
        * substation_type: The classification and voltage level of the substation
        * capacity: The rated capacity of the connection at this substation
        * latitude: The geographic latitude of the substation
        * longitude: The geographic longitude of the substation
        * projects: Array of project objects, each containing:
          + distance: The distance from the substation to that project

- Format your response ONLY as a clean JSON object with these exact field names.
- Do not include any explanatory text before or after the JSON.
- If any information is not available in the summaries, use "Not available" for string fields, 0 for numeric fields, or an empty array for lists.
- Ensure the JSON is properly formatted and valid.
- IMPORTANT: The plants_count should reflect the exact number of distinct power plant sites, not generating units. Each plant site may use one or more generation technologies listed in plant_types.
- For plant-level details, ensure all nested arrays and objects follow the exact structure shown in the example.

Example format:
```json
{{
  "cfpp_type": "Private",
  "country_name": "India",
  "currency_in": "INR",
  "financial_year": "04-03",
  "organization_name": "Adani Power Limited",
  "plants_count": 6,
  "plant_types": ["Coal", "Solar", "Wind"],
  "ppa_flag": "Plant-level",
  "province": "Gujarat",
  "name": "Mundra Thermal Power Station",
  "plant_id": 101,
  "plant_type": "Coal",
  "lat": "22.8235",
  "long": "69.5323",
  "plant_address": "Mundra, Gujarat, India",
  "units_id": ["Unit 1", "Unit 2", "Unit 3", "Unit 4", "Unit 5"],
  "ppa_details": [
    {{
      "capacity": "1000",
      "capacity_unit": "MW",
      "start_date": "2010-03-01",
      "end_date": "2035-03-01",
      "tenure": "25",
      "tenure_type": "Years",
      "respondents": [
        {{
          "name": "Gujarat Urja Vikas Nigam Limited",
          "capacity": "1000",
          "currency": "INR",
          "price": "2.35",
          "price_unit": "INR/kWh"
        }}
      ]
    }}
  ],
  "grid_connectivity_maps": [
    {{
      "details": [
        {{
          "substation_name": "Mundra HVDC Station",
          "substation_type": "HVDC 500kV",
          "capacity": "2500",
          "latitude": "22.8401",
          "longitude": "69.5501",
          "projects": [
            {{
              "distance": "3.5 km"
            }}
          ]
        }}
      ]
    }}
  ]
}}
```

User Context:
- {research_topic}

Summaries:
{summaries}"""
