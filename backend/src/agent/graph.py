import os
import uuid
import time
from typing import List

from agent.tools_and_schemas import SearchQueryList, Reflection, PlantLevelInfo, UnitLevelInfo
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client

from agent.state import (
    OverallState,
    UnitState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
    merge_unit_matrices,
    merge_unit_flags,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
    unit_query_writer_instructions,
    unit_web_searcher_instructions,
    unit_reflection_instructions,
    unit_answer_instructions,
)
from langchain_google_genai import ChatGoogleGenerativeAI
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)
from agent.image_extraction import extract_and_upload_images
from agent.json_s3_storage import (
    store_organization_data,
    store_plant_data, 
    store_unit_data,
    get_plant_s3_urls,
    check_s3_connection,
    sanitize_plant_name
)
from agent.fallback_calculations import FallbackCalculationEngine

def filter_plant_data_to_template(plant_data: dict, session_id: str = "unknown") -> dict:
    """
    Filter plant data to only include fields from the original plant_level.json template
    """
    # Define the exact fields that should be in plant_level.json
    allowed_plant_fields = {
        "sk", "pk", "annual_operational_hours", "auxiliary_power_consumed", 
        "closure_year", "commencement_date", "cuf", "gross_power_generation",
        "grid_connectivity_maps", "installed_bess_capacity", "installed_bess_capacity_unit",
        "installed_biomass_capacity", "installed_biomass_capacity_unit", 
        "installed_solar_capacity", "installed_solar_capacity_unit",
        "installed_wind_capacity", "installed_wind_capacity_unit",
        "latitude", "longitude", "mandatory_closure", "name", "plant_address",
        "plant_id", "plant_images", "plant_lifetime", "plant_type",
        "potential_reference", "ppa_details", "remaining_useful_life",
        "total_installed_capacity", "total_installed_capacity_unit", "units_id",
        "metadata"  # Allow metadata as it's added by the system
    }
    
    # Filter the data
    filtered_data = {}
    removed_fields = []
    
    for key, value in plant_data.items():
        if key in allowed_plant_fields:
            filtered_data[key] = value
        else:
            removed_fields.append(key)
    
    if removed_fields:
        print(f"[Session {session_id}] 🧹 Removed extra fields from plant data: {removed_fields}")
    
    return filtered_data

def ensure_numeric_values(plant_data: dict, session_id: str = "unknown") -> dict:
    """
    Ensure specific fields in plant data are numeric (not strings)
    """
    numeric_fields = [
        "total_installed_capacity",
        "installed_solar_capacity",
        "installed_wind_capacity", 
        "installed_biomass_capacity",
        "installed_bess_capacity",
        "latitude",
        "longitude"
    ]
    
    # Process simple numeric fields
    for field in numeric_fields:
        if field in plant_data and plant_data[field]:
            if isinstance(plant_data[field], str):
                try:
                    # Convert string to float but preserve decimal values
                    plant_data[field] = float(plant_data[field].replace("%", "").replace(",", ""))
                except (ValueError, TypeError):
                    print(f"[Session {session_id}] ⚠️ Could not convert {field} to numeric: {plant_data[field]}")
                    plant_data[field] = 0
    
    # Process array fields with value/year structure
    array_fields = ["auxiliary_power_consumed", "cuf", "gross_power_generation"]
    for field in array_fields:
        if field in plant_data and isinstance(plant_data[field], list):
            for item in plant_data[field]:
                if isinstance(item, dict) and "value" in item:
                    if isinstance(item["value"], str):
                        try:
                            # Convert string to float but preserve decimal values
                            item["value"] = float(item["value"].replace("%", "").replace(",", ""))
                        except (ValueError, TypeError):
                            print(f"[Session {session_id}] ⚠️ Could not convert {field} value to numeric: {item['value']}")
                            item["value"] = 0
                if isinstance(item, dict) and "year" in item:
                    if isinstance(item["year"], str):
                        try:
                            item["year"] = int(item["year"])  # Years should be integers
                        except (ValueError, TypeError):
                            pass
    
    # Process PPA details
    if "ppa_details" in plant_data and isinstance(plant_data["ppa_details"], list):
        for ppa in plant_data["ppa_details"]:
            if isinstance(ppa, dict):
                # Convert capacity
                if "capacity" in ppa and isinstance(ppa["capacity"], str):
                    try:
                        ppa["capacity"] = float(ppa["capacity"].replace(",", ""))
                    except (ValueError, TypeError):
                        pass
                
                # Convert respondents data
                if "respondents" in ppa and isinstance(ppa["respondents"], list):
                    for resp in ppa["respondents"]:
                        if isinstance(resp, dict):
                            for field in ["capacity", "price", "tenure"]:
                                if field in resp and isinstance(resp[field], str):
                                    try:
                                        resp[field] = float(resp[field].replace(",", ""))
                                    except (ValueError, TypeError):
                                        pass
    
    return plant_data

def extract_numeric_value(value):
    """Extract numeric value from various formats"""
    if isinstance(value, (int, float)):
        return value  # Keep as is, don't convert to int
    
    if isinstance(value, str):
        # Remove common non-numeric characters
        cleaned = value.replace(",", "").replace("MW", "").replace("MWh", "").replace("GW", "").replace("%", "").strip()
        try:
            return float(cleaned)  # Keep as float, don't convert to int
        except:
            return 0
    
    return 0

def get_auxiliary_power_percentage(plant_type):
    """Get typical auxiliary power consumption percentage by plant type"""
    auxiliary_power_standards = {
        "solar": 1.0,
        "wind": 1.5,
        "thermal": 5.0,
        "biomass": 6.0,
        "coal": 6.5,
        "gas": 2.5,
        "hydro": 1.0
    }
    return auxiliary_power_standards.get(plant_type, 5.0)

def validate_country_location(plant_name, country_name, research_summaries):
    """Validate that the country matches the specific power plant mentioned"""
    # List of common countries for quick validation
    valid_countries = [
        "India", "United States", "China", "Japan", "Germany", "United Kingdom", 
        "France", "Canada", "Australia", "Brazil", "Mexico", "South Africa",
        "Saudi Arabia", "United Arab Emirates", "Russia", "Turkey", "Spain",
        "Italy", "Netherlands", "South Korea", "Indonesia", "Thailand", "Vietnam",
        "Poland", "Czech Republic", "Ukraine", "Argentina", "Chile", "Colombia",
        "Peru", "Egypt", "Morocco", "Nigeria", "Kenya", "Ghana", "Pakistan",
        "Bangladesh", "Sri Lanka", "Philippines", "Malaysia", "Singapore"
    ]
    
    # Basic validation - check if it's a recognized country
    if country_name not in valid_countries and country_name.strip() != "":
        print(f"⚠️  Warning: '{country_name}' may not be a standard country name for plant: {plant_name}")
    
    # Check if plant name and country appear together in summaries
    if research_summaries and plant_name and country_name:
        summaries_text = research_summaries.lower()
        plant_name_lower = plant_name.lower()
        country_name_lower = country_name.lower()
        
        # Simple co-occurrence check
        if plant_name_lower in summaries_text and country_name_lower in summaries_text:
            print(f"✅ Country validation: '{country_name}' appears with '{plant_name}' in research data")
            return True
        else:
            print(f"⚠️  Warning: Limited evidence for '{country_name}' being the location of '{plant_name}'")
            return False
    
    return True

# Parallel processing imports would go here when implemented

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def initialize_session(state: OverallState) -> OverallState:
    """Initialize a new research session with proper state management.
    
    This function ensures each research session starts with clean state
    and generates a unique session ID for tracking.
    """
    # Generate a unique session ID
    session_id = str(uuid.uuid4())[:8] + str(int(time.time()))[-4:]
    
    print(f"🚀 INITIALIZE_SESSION CALLED - New research session: {session_id}")
    print(f"🔍 DEBUG: Input state keys: {list(state.keys())}")
    print(f"🔍 DEBUG: Messages count: {len(state.get('messages', []))}")
    
    # Debug messages in detail
    messages = state.get("messages", [])
    print(f"🔍 DEBUG: Raw messages type: {type(messages)}")
    print(f"🔍 DEBUG: Raw messages content: {messages}")
    
    for i, msg in enumerate(messages):
        msg_type = type(msg).__name__
        msg_content = getattr(msg, 'content', 'NO_CONTENT')
        msg_type_attr = getattr(msg, 'type', 'NO_TYPE')
        print(f"🔍 DEBUG: Message {i}: Type={msg_type}, msg.type={msg_type_attr}, Content='{msg_content}'")
    
    # Get the research topic from messages
    research_topic = get_research_topic(state["messages"]) if state.get("messages") else ""
    
    print(f"📝 Research topic extracted: '{research_topic}'")
    
    # Initialize S3 JSON storage tracking
    plant_s3_urls = get_plant_s3_urls(research_topic, session_id)
    plant_name_sanitized = sanitize_plant_name(research_topic)
    
    # Test S3 connection
    s3_ready = check_s3_connection(session_id)
    
    print(f"🗂️ S3 folder will be: {plant_name_sanitized}")
    print(f"🔗 S3 connection: {'✅ Ready' if s3_ready else '❌ Failed'}")
    print(f"✅ INITIALIZE_SESSION COMPLETE - Session {session_id}")
    
    # Return clean initialized state (no progress messages in UI)
    return {
        "messages": state.get("messages", []),
        "session_id": session_id,
        "research_topic": research_topic,  # Store research topic in state
        "search_phase": 1,  # Start with organization-level
        "research_loop_count": 0,
        "web_research_result": [],
        "search_query": [],
        "sources_gathered": [],
        "org_level_complete": False,
        "continue_research": False,
        "phase_complete": False,
        "initial_search_query_count": state.get("initial_search_query_count", 5),
        "max_research_loops": state.get("max_research_loops", 3),
        "reasoning_model": state.get("reasoning_model", ""),
        # S3 JSON Storage initialization  
        "plant_name_for_s3": research_topic,
        "s3_json_urls": plant_s3_urls,
        "json_storage_complete": {"organization": False, "plant": False, "units": {}},
        "json_storage_errors": [],
    }


def extract_images_parallel(state: OverallState) -> OverallState:
    """
    LangGraph node that extracts images for the power plant in parallel.
    
    This function runs concurrently with the main research pipeline to gather
    relevant images for the power plant and upload them to S3.
    """
    session_id = state.get("session_id", "unknown")
    research_topic = state.get("research_topic", "")
    
    # Fallback: If research_topic not in state yet, extract from messages
    if not research_topic:
        research_topic = get_research_topic(state.get("messages", []))
        print(f"[Session {session_id}] ⚠️ Image extraction using fallback research topic: '{research_topic}'")
    else:
        print(f"[Session {session_id}] ✅ Image extraction using state research topic: '{research_topic}'")
    
    print(f"[Session {session_id}] 🖼️ Starting parallel image extraction for: {research_topic}")
    
    try:
        # Extract and upload images
        s3_urls = extract_and_upload_images(research_topic, session_id)
        
        if s3_urls:
            print(f"[Session {session_id}] ✅ Image extraction successful: {len(s3_urls)} images uploaded")
            return {
                "image_extraction_complete": True,
                "s3_image_urls": s3_urls,
                "image_extraction_error": ""
            }
        else:
            print(f"[Session {session_id}] ⚠️ No images found or uploaded")
            return {
                "image_extraction_complete": True,
                "s3_image_urls": [],
                "image_extraction_error": "No images found"
            }
            
    except Exception as e:
        error_msg = f"Image extraction failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "image_extraction_complete": True,
            "s3_image_urls": [],
            "image_extraction_error": error_msg
        }


# Nodes
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates search queries based on the User's question."""
    configurable = Configuration.from_runnable_config(config)

    # Check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries
    
    # Get the search phase from the state
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== QUERY GENERATION START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Current state messages count: {len(state.get('messages', []))}")
    
    # Initialize Gemini 2.0 Flash
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)

    # Get current date and research topic from state
    current_date = get_current_date()
    research_topic = state.get("research_topic", "")
    if not research_topic:
        # Fallback: extract from messages if not in state
        research_topic = get_research_topic(state["messages"])
        print(f"[Session {session_id}] ⚠️ FALLBACK: Research topic extracted from messages: '{research_topic}'")
    else:
        print(f"[Session {session_id}] ✅ Research topic from state: '{research_topic}'")
    
    # Validate research topic
    if not research_topic or not research_topic.strip():
        print(f"[Session {session_id}] ❌ ERROR: Empty research topic detected!")
        print(f"[Session {session_id}] Messages in state: {len(state.get('messages', []))}")
        for i, msg in enumerate(state.get('messages', [])):
            print(f"[Session {session_id}] Message {i}: {type(msg).__name__} - {str(msg.content)[:100]}")
        
        # Return error queries to prevent infinite loop of bad queries
        return {
            "query_list": [{"query": "ERROR: No research topic provided", "rationale": "Empty research topic"}],
            "research_loop_count": state.get("research_loop_count", 0),
            "number_of_ran_queries": state.get("number_of_ran_queries", 0),
            "search_phase": search_phase,
            "session_id": session_id,  # Preserve session ID
            "research_topic": research_topic,  # Preserve research topic
        }
    
    if search_phase == 1:
        # Organization-level query generation for new org_level.json template
        formatted_prompt = f"""Your goal is to generate sophisticated search queries to gather organization-level information about a power plant according to the org_level.json template.

Instructions:
- Generate search queries to find detailed information about the power plant named in the research topic.
- Focus on queries that will help extract organization-level information for the org_level.json template including:
  1. organisation_name: Official name of the organization/company that owns or operates the power plant
  2. country_name: Full official country name where the power plant is located
  3. currency_in: ISO 4217 currency code of that country
  4. financial_year: Fiscal year period in MM-MM format for that country
  5. technology_type: What types of power generation technologies are used (determine if Renewable Energy, Biomass, SMR, Green Hydrogen Plant are enabled true/false)
  6. future_capacity: Future expansion plans for next 5-10 years including planned capacity additions by technology type

- BE VERY SPECIFIC about the exact power plant name and location to avoid confusion with similarly named plants
- Include the exact plant name in quotes in your search queries
- Add location context to distinguish from other plants with similar names
- Don't produce more than {state.get("initial_search_query_count", 5)} queries.
- Focus on getting up-to-date information as of {current_date}.

Format: 
- Format your response as a JSON object with these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Research Topic: {research_topic}

IMPORTANT: Always include the exact power plant name from the research topic in quotes to ensure precise results and avoid confusion with other plants.
"""
    elif search_phase == 2:
        # Plant-level query generation for new plant_level.json template
        formatted_prompt = f"""Your goal is to generate HIGHLY SPECIFIC search queries to gather ACCURATE plant-level information according to the plant_level.json template.

CRITICAL ACCURACY REQUIREMENTS:
- BE EXTREMELY PRECISE about the exact power plant name and location
- Include exact plant name in quotes in ALL queries to avoid confusion
- Add location/city/state context to distinguish from other plants
- Search for OFFICIAL technical documentation, regulatory filings, company reports
- Target CREDIBLE sources: government databases, regulatory bodies, company websites

Focus on queries that will help extract ACCURATE plant-level information:
  1. name: EXACT official name (search: "exact plant name" official name technical specifications)
  2. plant_type: SPECIFIC technology type (search: "exact plant name" technology type solar wind thermal biomass)  
  3. plant_address: PRECISE address (search: "exact plant name" address location district state)
  4. latitude/longitude: EXACT GPS coordinates (search: "exact plant name" coordinates latitude longitude GPS location)
  5. total_installed_capacity: EXACT capacity in MW (search: "exact plant name" capacity MW installed capacity technical specifications)
  6. commencement_date: EXACT commissioning date (search: "exact plant name" commissioning date commercial operation date COD)
  7. plant_lifetime: ACTUAL plant lifetime (search: "exact plant name" plant lifetime design life operational period)
  8. remaining_useful_life: CALCULATED end-of-life (search: "exact plant name" remaining life decommissioning closure date)
  9. units_id: SPECIFIC unit identifiers (search: "exact plant name" units turbines generators unit names)
  10. auxiliary_power_consumed: ACTUAL yearly auxiliary power data (search: "exact plant name" auxiliary power consumption operational data)
  11. cuf: ACTUAL Capacity Utilization Factor by year (search: "exact plant name" CUF capacity utilization factor performance data)
  12. gross_power_generation: ACTUAL yearly generation data (search: "exact plant name" power generation annual generation electricity produced)
  13. ppa_details: SPECIFIC PPA information (search: "exact plant name" PPA "power purchase agreement" offtake contract pricing tariff)
  14. grid_connectivity_maps: SPECIFIC grid connection details (search: "exact plant name" transmission substation grid connection PGCIL voltage level)
  15. installed capacity: TECHNOLOGY-SPECIFIC capacity breakdown (search: "exact plant name" solar capacity wind capacity biomass capacity BESS)
  16. closure_year: ACTUAL closure/decommissioning year (search: "exact plant name" closure decommissioning shutdown date)

SEARCH STRATEGY:
- Use EXACT plant name in quotes: "{research_topic}"
- Add location context to avoid confusion with similar named plants
- Target official sources: regulatory filings, company reports, government databases
- Search for recent performance data (last 5 years)
- Look for technical specifications documents
- Don't produce more than {state.get("initial_search_query_count", 5)} queries.
- Focus on getting up-to-date information as of {current_date}.

Format: 
- Format your response as a JSON object with these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Research Topic: {research_topic}
"""
    else:
        # Default case - should not happen in two-phase processing
        formatted_prompt = f"""Generate basic search queries for the research topic: {research_topic}"""
    
    # Generate the search queries with error handling
    try:
        result = structured_llm.invoke(formatted_prompt)
        print(f"[Session {session_id}] Generated {len(result.query)} queries for phase {search_phase}")
        
        # Debug the generated queries
        for i, query_obj in enumerate(result.query):
            if isinstance(query_obj, dict):
                query_text = query_obj.get("query", str(query_obj))
            else:
                query_text = str(query_obj)
            print(f"[Session {session_id}] Query {i+1}: {query_text[:100]}")
        
        # Validate that queries are not all identical nonsense
        query_texts = []
        for query_obj in result.query:
            if isinstance(query_obj, dict):
                query_texts.append(query_obj.get("query", str(query_obj)))
            else:
                query_texts.append(str(query_obj))
        
        # Check for repeated nonsensical queries
        unique_queries = set(query_texts)
        if len(unique_queries) == 1 and "What is the name of the power plant" in list(unique_queries)[0]:
            print(f"[Session {session_id}] ❌ ERROR: LLM generated nonsensical repeated queries!")
            print(f"[Session {session_id}] Research topic was empty, causing bad prompt")
            
            # Return a single error query instead of many bad ones
            return {
                "query_list": [{"query": f"ERROR: Cannot generate queries without research topic. Phase: {search_phase}", "rationale": "Missing research topic"}],
                "research_loop_count": state.get("research_loop_count", 0),
                "number_of_ran_queries": state.get("number_of_ran_queries", 0),
                "search_phase": search_phase,
                "session_id": session_id,  # Preserve session ID
                "research_topic": research_topic,  # Preserve research topic
            }
        
        # Return the query list and preserve state
        return {
            "query_list": result.query,
            "research_loop_count": state.get("research_loop_count", 0),
            "number_of_ran_queries": state.get("number_of_ran_queries", 0),
            "search_phase": search_phase,
            "session_id": session_id,  # Preserve session ID
            "research_topic": research_topic,  # Preserve research topic
        }
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ ERROR in query generation: {str(e)}")
        return {
            "query_list": [{"query": f"ERROR: Query generation failed: {str(e)}", "rationale": "System error"}],
            "research_loop_count": state.get("research_loop_count", 0),
            "number_of_ran_queries": state.get("number_of_ran_queries", 0),
            "search_phase": search_phase,
            "session_id": session_id,  # Preserve session ID
            "research_topic": research_topic,  # Preserve research topic
        }


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using the native Google Search API tool."""
    configurable = Configuration.from_runnable_config(config)
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== WEB RESEARCH START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    
    # Get queries from state - could be from query_list or search_query
    queries = state.get("query_list", [])
    if not queries:
        queries = state.get("search_query", [])
    if not queries:
        queries = []
    
    print(f"[Session {session_id}] Queries: {queries}")
    
    # Get the current research topic (should already be extracted in state)
    research_topic = state.get("research_topic", "")
    if not research_topic:
        # Fallback: extract from messages if not in state
        research_topic = get_research_topic(state.get("messages", []))
        print(f"[Session {session_id}] ⚠️ FALLBACK: Research topic extracted from messages: '{research_topic}'")
    else:
        print(f"[Session {session_id}] ✅ Research topic from state: '{research_topic}'")
    
    # Customize the prompt based on the search phase
    if search_phase == 1:
        # Phase 1: Focus on organization-level information for org_level.json template
        formatted_prompt = f"""Conduct targeted Google Searches to gather organization-level information according to the org_level.json template.

Instructions:
- The current date is {get_current_date()}.
- Search for and extract organization-level details about the power plant according to org_level.json template:
  1. organisation_name: Official name of the organization/company that owns or operates the power plant
  2. country_name: Full official country name where the organization or plant is located
  3. currency_in: ISO 4217 currency code of that power plant country
  4. financial_year: Fiscal year period in MM-MM format
  5. technology_type: Determine which technologies are used and set enabled true/false for:
     - Renewable Energy (solar, wind, hydro, etc.)
     - Biomass (biomass cofiring, dedicated biomass plants)
     - SMR (Small Modular Reactors)
     - Green Hydrogen Plant (green hydrogen production)
  6. future_capacity: Future expansion plans for next 5-10 years including:
     - Planned capacity additions by technology type (wind/solar/BESS/Wind+Solar/hydro/pumped hydro/geothermal/biomass)
     - Target years for capacity additions
     - Capacity amounts in MW

- Be precise and factual. Only include information from credible sources.
- ENSURE LOCATION ACCURACY: Verify that the country information specifically refers to the exact power plant mentioned in the research topic
- Cross-reference multiple sources to confirm the correct country location
- If there are multiple plants with similar names in different countries, clearly identify which one is being referenced
- Track all sources meticulously for citation purposes.

Research Topic: {research_topic}

CRITICAL: Verify the country location is specifically for the power plant named: "{research_topic}" - not a different plant with a similar name.
"""
    elif search_phase == 2:
        # Phase 2: Focus on plant-level information for plant_level.json template
        formatted_prompt = f"""Conduct HIGHLY TARGETED Google Searches to gather ACCURATE plant-level information according to the plant_level.json template.

CRITICAL ACCURACY REQUIREMENTS:
- The current date is {get_current_date()}.
- ONLY extract information that is SPECIFICALLY about the power plant: "{research_topic}"
- VERIFY that all data corresponds to the EXACT plant name and location
- CROSS-REFERENCE multiple sources to confirm accuracy
- DISTINGUISH between different plants with similar names
- PRIORITIZE official sources: regulatory filings, company reports, government databases

Search for and extract ACCURATE plant-level details:
  1. name: EXACT official name (verify this is the correct plant, not a similar named one)
  2. plant_type: SPECIFIC technology type (solar/wind/thermal/biomass/hydro/nuclear - be precise)
  3. plant_address: COMPLETE precise address (District/city, State, Country)
  4. latitude/longitude: EXACT GPS coordinates (verify coordinates match the plant location)
  5. total_installed_capacity: EXACT capacity in MW (verify this is the correct plant's capacity)
  6. commencement_date: EXACT commissioning date (yyyy-mm-ddThh:mm:ss.000Z format)
  7. plant_lifetime: ACTUAL design lifetime in years (typically 25-30 years)
  8. remaining_useful_life: CALCULATED end-of-life date based on commissioning + lifetime
  9. units_id: SPECIFIC unit identifiers (Unit-1, Unit-2, etc. - actual unit names)
  10. auxiliary_power_consumed: ACTUAL yearly auxiliary power data (percentage values by year)
  11. cuf: ACTUAL Capacity Utilization Factor by year (percentage values for last 5 years)
  12. gross_power_generation: ACTUAL yearly generation data (MWh/GWh values for last 5 years)
  13. ppa_details: SPECIFIC PPA information (exact contract details, pricing, counterparties, dates)
  14. grid_connectivity_maps: SPECIFIC grid connection details (exact substation names, voltage levels, capacities)
  15. installed capacity: TECHNOLOGY-SPECIFIC capacity breakdown (exact MW values for solar/wind/biomass/BESS)
  16. closure_year: ACTUAL planned closure/decommissioning year

VERIFICATION REQUIREMENTS:
- Verify plant name and location match exactly
- Cross-check capacity figures across multiple sources
- Confirm operational dates and status
- Validate performance data (CUF, generation) is realistic for the plant type
- Check that PPA details are current and accurate
- Ensure grid connection details are specific to this plant
- Look for the most recent data available (prioritize last 5 years)

SOURCE PRIORITIES:
1. Government/regulatory databases (CEA, CERC, State ERCs)
2. Company annual reports and investor presentations
3. Environmental clearance documents
4. Technical specifications from equipment manufacturers
5. Industry reports and trade publications
6. News articles (only for recent updates, not primary technical data)

- Search for multiple plants if they exist under the same organization
- Track all sources meticulously for citation purposes.
- If conflicting information is found, prioritize the most recent and authoritative source

Research Topic: {research_topic}
Research Queries: {queries}
"""
    else:
        # Default case
        formatted_prompt = f"""Conduct basic web search for: {research_topic}
Research Queries: {queries}"""

    try:
        # Uses the google genai client
        response = genai_client.models.generate_content(
            model=configurable.query_generator_model,
            contents=formatted_prompt,
            config={
                "tools": [{"google_search": {}}],
                "temperature": 0,
            },
        )
        
        # Get grounding chunks safely
        grounding_chunks = None
        if (response and hasattr(response, 'candidates') and response.candidates 
            and hasattr(response.candidates[0], 'grounding_metadata') 
            and response.candidates[0].grounding_metadata
            and hasattr(response.candidates[0].grounding_metadata, 'grounding_chunks')):
            grounding_chunks = response.candidates[0].grounding_metadata.grounding_chunks
        
        # resolve the urls to short urls for saving tokens and time
        resolved_urls = resolve_urls(grounding_chunks, session_id)
        
        # Gets the citations and adds them to the generated text
        citations = get_citations(response, resolved_urls)
        modified_text = insert_citation_markers(response.text, citations)
        sources_gathered = [item for citation in citations for item in citation["segments"]]
        
        print(f"[Session {session_id}] Successfully completed web research for phase {search_phase}")
        
    except Exception as e:
        import traceback
        print(f"[Session {session_id}] Error in web_research: {str(e)}")
        print(f"[Session {session_id}] Traceback: {traceback.format_exc()}")
        modified_text = f"I encountered an error while researching '{research_topic}'. Error: {str(e)}"
        sources_gathered = []

    return {
        "sources_gathered": sources_gathered,
        "search_query": queries,
        "web_research_result": [modified_text],
        "search_phase": state.get("search_phase"),
    }


def reflection(state: OverallState, config: RunnableConfig) -> dict:
    """LangGraph node that identifies knowledge gaps and generates potential follow-up queries."""
    configurable = Configuration.from_runnable_config(config)
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model") or configurable.reasoning_model
    search_phase = state.get("search_phase", 1)
    
    current_date = get_current_date()
    session_id = state.get("session_id", "unknown")
    
    # Get research topic from state or fallback to messages
    research_topic = state.get("research_topic", "")
    if not research_topic:
        research_topic = get_research_topic(state.get("messages", []))
    
    print(f"[Session {session_id}] ===== REFLECTION START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Research loop count: {state['research_loop_count']}")
    print(f"[Session {session_id}] Research topic: {research_topic}")
    
    # Combine all web research results so far
    all_summaries = "\n\n".join(state.get("web_research_result", []))
    
    # Format the prompt based on the search phase
    if search_phase == 1:
        formatted_prompt = f"""Analyze the organization-level research findings and determine if additional research is needed.

Current findings:
{all_summaries}

Instructions:
- Review the research findings for organization-level information according to org_level.json template
- Identify any missing critical organization-level fields
- If important organization-level information is missing, generate 1-2 specific follow-up queries
- If sufficient organization-level information has been gathered, indicate research is complete

Current date: {current_date}
Max research loops: {state.get("max_research_loops", 3)}
Current loop: {state["research_loop_count"]}

Required organization fields to check:
- organisation_name: Official name of the organization/company that owns or operates the power plant
- country_name: Full official country name
- currency_in: ISO 4217 currency code
- financial_year: Fiscal year period in MM-MM format  
- technology_type: Enabled status for Renewable Energy, Biomass, SMR, Green Hydrogen Plant
- future_capacity: Future expansion plans for next 5-10 years with technology types and capacity amounts
"""
    elif search_phase == 2:
        formatted_prompt = f"""Analyze the plant-level research findings and determine if additional ACCURATE research is needed.

Current findings:
{all_summaries}

CRITICAL ACCURACY REQUIREMENTS:
- ONLY consider information that is specifically about the power plant: "{research_topic}"
- VERIFY that all findings correspond to the exact plant name and location
- IDENTIFY any conflicting or inconsistent information
- PRIORITIZE data quality over quantity

Instructions:
- Review the research findings for ACCURATE plant-level information according to plant_level.json template
- Identify any missing CRITICAL plant-level fields that are essential for accurate plant data
- Check if existing data is consistent and verifiable
- If important plant-level information is missing or inconsistent, generate 1-2 SPECIFIC follow-up queries
- If sufficient ACCURATE plant-level information has been gathered, indicate research is complete

Current date: {current_date}
Max research loops: {state.get("max_research_loops", 3)}
Current loop: {state["research_loop_count"]}

CRITICAL plant fields to check for ACCURACY:
- name: Must exactly match "{research_topic}"
- plant_type: Must be specific technology type (solar/wind/thermal/biomass/hydro/nuclear/hybrid)
- plant_address: Must be complete and accurate address
- latitude/longitude: Must be verified coordinates for the plant location
- total_installed_capacity: Must be verified MW capacity
- commencement_date: Must be verified commissioning date
- units_id: Must be verified unit identifiers
- auxiliary_power_consumed: Must be verified percentage values by year
- cuf: Must be verified and realistic percentage values for plant type
- gross_power_generation: Must be verified MWh/GWh values
- ppa_details: Must be verified contract details with complete nested structure
- grid_connectivity_maps: Must be verified grid connection details

QUALITY CHECKS:
- Is the data specifically about the plant named in research topic?
- Are capacity values realistic for the plant type?
- Are performance metrics (CUF, generation) consistent with plant type and size?
- Are dates and timelines logical and consistent?
- Are location details (address, coordinates) consistent?
"""
    else:
        formatted_prompt = f"""Analyze the research findings and determine if additional research is needed: {all_summaries}"""

    # Add specific instructions to avoid structured output issues
    formatted_prompt += """

IMPORTANT: Answer these specific questions directly:
1. Is the information sufficient? (yes/no)
2. What information is missing or needs clarification?
3. What specific follow-up query would help address these gaps? (provide one clear query)

DO NOT format your response as JSON or any structured format.
"""

    # Initialize LLM
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    # Get raw response
    try:
        response = llm.invoke(formatted_prompt)
        response_text = response.content
        
        print(f"[Session {session_id}] 📝 Reflection response received")
        
        # Simple text-based analysis - no structured output
        is_sufficient = False
        if "sufficient" in response_text.lower() and "yes" in response_text.lower()[:200]:
            is_sufficient = True
        
        # Extract a follow-up query - look for a question mark
        import re
        follow_up_query = ""
        
        # Try to find a question
        questions = re.findall(r'[^.!?]*\?', response_text)
        if questions:
            # Use the longest question as it's likely the most detailed
            follow_up_query = max(questions, key=len).strip()
        
        # If no question found, look for numbered or bulleted items
        if not follow_up_query:
            # Look for numbered items
            numbered_items = re.findall(r'\d+\.\s*(.*?)(?=\d+\.|$)', response_text, re.DOTALL)
            if numbered_items and len(numbered_items) > 2:  # The third item is likely the query
                follow_up_query = numbered_items[2].strip()
            
            # Look for bulleted items
            if not follow_up_query:
                bulleted_items = re.findall(r'[-*•]\s*(.*?)(?=[-*•]|$)', response_text, re.DOTALL)
                if bulleted_items:
                    for item in bulleted_items:
                        if len(item.strip()) > 20:  # Likely a substantive query
                            follow_up_query = item.strip()
                            break
        
        # If still no query found but we need more research, create a generic one
        if not follow_up_query and not is_sufficient:
            follow_up_query = f"What are the specific details about {research_topic} regarding its capacity, location, and operational status?"
        
        # Determine if we should continue research
        continue_research = not is_sufficient and state["research_loop_count"] < state.get("max_research_loops", 3)
        
        print(f"[Session {session_id}] Continue research: {continue_research}")
        if follow_up_query:
            print(f"[Session {session_id}] Follow-up query: {follow_up_query}")
        
        # Return a simple dictionary - no structured output
        return {
            "continue_research": continue_research,
            "search_query": [follow_up_query] if follow_up_query else [],
            "research_loop_count": state["research_loop_count"],
            "search_phase": search_phase,
        }
        
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Error in reflection: {str(e)}")
        # Fallback in case of any error
        return {
            "continue_research": state["research_loop_count"] < state.get("max_research_loops", 3),
            "search_query": [f"What are the key details about {research_topic}?"],
            "research_loop_count": state["research_loop_count"],
            "search_phase": search_phase,
        }


def finalize_answer_parallel(state: OverallState, config: RunnableConfig):
    """Enhanced LangGraph node that performs parallel image extraction and JSON processing."""
    configurable = Configuration.from_runnable_config(config)
    
    # Initialize LLM with specific parameters to avoid structured output issues
    llm = ChatGoogleGenerativeAI(
        model=configurable.reasoning_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
        convert_system_message_to_human=True  # This helps avoid the "thought" field issue
    )
    
    # Get basic info
    current_date = get_current_date()
    session_id = state.get("session_id", "unknown")
    search_phase = state.get("search_phase", 1)
    
    research_topic = state.get("research_topic", "")
    # Fallback: If research_topic not in state, extract from messages
    if not research_topic:
        research_topic = get_research_topic(state["messages"])
        print(f"[Session {session_id}] ⚠️ Finalize using fallback research topic: '{research_topic}'")
    
    print(f"[Session {session_id}] ===== FINALIZE ANSWER START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Research topic: {research_topic}")
    
    # Combine all research summaries
    summaries = "\n\n".join(state.get("web_research_result", []))
    
    if search_phase == 1:
        # Organization-level extraction according to org_level.json template
        org_prompt = f"""Extract and format organization-level information according to the org_level.json template.

Instructions:
- The current date is {current_date}.
- Extract organization-level information from the research summaries.
- CRITICAL: Ensure the country_name specifically refers to the power plant mentioned in the research topic: "{research_topic}"
- Double-check that you're not confusing this plant with another plant that has a similar name but is located in a different country
- Format response EXACTLY as the org_level.json template with these fields:

{{
  "pk": null,
  "sk": "scraped#org_details",
  "organisation_name": "organisation name of the power plant",
  "country_name": "full name of the country where the organization or plant is located",
  "currency_in": "ISO 4217 currency code of that power plant country",
  "financial_year": "Fiscal year period in MM-MM format",
  "currency_convert_to": null,
  "currency_listed": [],
  "technology_type": [
    {{
      "enabled": "true/false",
      "name": "Renewable Energy"
    }},
    {{
      "enabled": "true/false",
      "name": "Biomass"
    }},
    {{
      "enabled": "true/false",
      "name": "SMR"
    }},
    {{
      "enabled": "true/false",
      "name": "Green Hydrogen Plant"
    }}
  ],
  "future_capacity": [
    {{
      "year": "for next 5 to 10 years",
      "type": "wind/solar/BESS/Wind+Solar/hydro/pumped hydro/geothermal/biomass",
      "capacity": "capacity in MW"
    }}
  ]
}}

- For technology_type array, set "enabled" to "true" if that technology is used by the organization, "false" if not
- For future_capacity array, include planned capacity expansions for next 5-10 years with specific years, technology types, and capacity amounts
- Keep static fields exactly as shown: "pk": null, "currency_convert_to": null, "currency_listed": []
- If information is not available, use empty strings for text fields or empty arrays for list fields
- Ensure JSON is properly formatted and valid
- Do not include explanatory text before or after the JSON
- Use ONLY the fields shown above, in this exact structure

Research Topic: {research_topic}

IMPORTANT FOR COUNTRY EXTRACTION:
- Only extract country information that is explicitly mentioned for the power plant: "{research_topic}"
- If multiple countries are mentioned in the summaries, make sure you identify which country specifically hosts this power plant
- Look for phrases like "located in", "situated in", "based in" followed by the country name
- Avoid confusion with parent company headquarters or other facilities

Summaries:
{summaries}
"""
        try:
            # Use a more robust approach to avoid structured output issues
            from langchain_core.messages import HumanMessage
            
            # Convert the prompt to a message
            message = HumanMessage(content=org_prompt)
            
            # Use invoke with explicit message format
            result = llm.invoke([message])
            final_content = result.content
            
            # Process organization data for S3 storage
            import json
            
            try:
                # Extract JSON from response
                print(f"[Session {session_id}] 🔍 Extracting organization JSON from response...")
                print(f"[Session {session_id}] Response length: {len(final_content)}")
                print(f"[Session {session_id}] First 200 chars: {final_content[:200]}")
                
                start_idx = final_content.find('{')
                end_idx = final_content.rfind('}') + 1
                print(f"[Session {session_id}] JSON boundaries: start={start_idx}, end={end_idx}")
                
                if start_idx != -1 and end_idx != -1:
                    json_str = final_content[start_idx:end_idx]
                    print(f"[Session {session_id}] Extracted JSON: {json_str[:200]}...")
                    
                    org_data = json.loads(json_str)
                    print(f"[Session {session_id}] ✅ JSON parsed successfully")
                    
                    # Set static sk field for organization level
                    org_data["sk"] = "scraped#org_details"
                    
                    # Validate country location
                    if "country_name" in org_data:
                        validate_country_location(research_topic, org_data["country_name"], summaries)
                    
                    # Store organization data to S3
                    current_s3_urls = state.get("s3_json_urls", {})
                    current_storage_status = state.get("json_storage_complete", {})
                    current_errors = state.get("json_storage_errors", [])
                    
                    plant_name_for_s3 = state.get("plant_name_for_s3", research_topic)
                    print(f"[Session {session_id}] 🏭 Storing organization data for: {plant_name_for_s3}")
                    
                    org_s3_url = store_organization_data(org_data, plant_name_for_s3, session_id)
                    
                    if org_s3_url:
                        print(f"[Session {session_id}] ✅ Organization JSON saved to S3: {org_s3_url}")
                        current_s3_urls["organization"] = org_s3_url
                        current_storage_status["organization"] = True
                    else:
                        print(f"[Session {session_id}] ❌ Failed to save organization JSON to S3")
                        current_errors.append("Failed to save organization JSON to S3")
                    
                    # Update final content with processed JSON
                    updated_json = json.dumps(org_data, indent=2)
                    final_content = f"ORGANIZATION-LEVEL INFORMATION:\n{updated_json}"
                else:
                    print(f"[Session {session_id}] ❌ No valid JSON found in organization response")
                    current_errors.append("No valid JSON found in organization response")
                    
            except Exception as e:
                print(f"⚠️ Could not process organization JSON: {e}")
                import traceback
                print(f"[Session {session_id}] Organization JSON processing error: {traceback.format_exc()}")
                current_s3_urls = state.get("s3_json_urls", {})
                current_storage_status = state.get("json_storage_complete", {})
                current_errors = state.get("json_storage_errors", [])
                current_errors.append(f"Organization-level extraction failed: {str(e)}")
            
            print("Successfully generated organization-level information")
            
        except Exception as e:
            import traceback
            print(f"Error in organization-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback organization response
            final_content = f"""ORGANIZATION-LEVEL INFORMATION:
{{
  "pk": null,
  "sk": "scraped#org_details",
  "country_name": "",
  "currency_in": "",
  "financial_year": "", 
  "currency_convert_to": null,
  "currency_listed": [],
  "technology_type": [
    {{
      "enabled": "false",
      "name": "Renewable Energy"
    }},
    {{
      "enabled": "false",
      "name": "Biomass"
    }},
    {{
      "enabled": "false",
      "name": "SMR"
    }},
    {{
      "enabled": "false",
      "name": "Green Hydrogen Plant"
    }}
  ],
  "future_capacity": []
}}"""
            
            current_s3_urls = state.get("s3_json_urls", {})
            current_storage_status = state.get("json_storage_complete", {})
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"Organization-level extraction failed: {str(e)}")
            
            print("Using fallback organization-level information")

        # Filter sources used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)

        # Return organization-level results
        print(f"[Session {session_id}] 📋 Final organization state:")
        print(f"[Session {session_id}] - S3 URLs: {current_s3_urls}")
        print(f"[Session {session_id}] - Storage status: {current_storage_status}")
        print(f"[Session {session_id}] - Errors: {current_errors}")
        
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
        }
    
    elif search_phase == 2:
        # Plant-level extraction according to plant_level.json template  
        plant_prompt = f"""Extract and format ACCURATE plant-level information according to the plant_level.json template.

CRITICAL ACCURACY REQUIREMENTS:
- The current date is {current_date}.
- ONLY extract information that is SPECIFICALLY about the power plant: "{research_topic}"
- VERIFY that all extracted data corresponds to the EXACT plant name and location
- DO NOT mix data from different plants with similar names
- CROSS-REFERENCE multiple sources before including any data
- If unsure about any data point, use empty string/array rather than guessing
- VALIDATE that numerical values are realistic for the plant type and size

Instructions:
- Extract plant-level information ONLY from the research summaries that specifically mention: "{research_topic}"
- If there are multiple power plants mentioned, return an array of plant objects with sequential plant_id
- If there is only one power plant, return a single plant object
- For each plant, extract ONLY VERIFIED information according to the plant_level.json template
- PRIORITIZE data from official sources (regulatory filings, company reports, government databases)
- IGNORE data that cannot be verified or seems inconsistent

Format response EXACTLY as the plant_level.json template with these fields:

{{
  "sk": "plant#[plant_type]#[plant_id]",
  "pk": null,
  "annual_operational_hours": 8760,
  "auxiliary_power_consumed": [
    {{
      "value": "The auxiliary (AUX) energy percentage",
      "year": "Year for which auxiliary power data is reported"
    }}
  ],
  "closure_year": "when the plant is closed down",
  "commencement_date": "The date of commercial operation (yyyy-mm-ddThh:mm:ss.000Z)",
  "cuf": [
    {{
      "value": "cuf value for the plant",
      "year": 2020
    }}
  ],
  "gross_power_generation": [
    {{
      "value": "Total energy generated by the plant",
      "year": "Year of the power generation data"
    }}
  ],
  "grid_connectivity_maps": [
    {{
      "details": [
        {{
          "capacity": "The rated capacity of the connection",
          "latitude": "The geographic latitude of the substation",
          "longitude": "The geographic longitude of the substation",
          "projects": [
            {{
              "distance": "The distance from the substation to project"
            }}
          ],
          "substation_name": "The official name of the substation",
          "substation_type": "The classification and voltage level of the substation"
        }}
      ],
      "name": "",
      "s3_url": ""
    }}
  ],
  "installed_bess_capacity": "installed battery capacity of the plant",
  "installed_bess_capacity_unit": "MWh",
  "installed_biomass_capacity": "installed biomass capacity of the plant",
  "installed_biomass_capacity_unit": "MW",
  "installed_solar_capacity": "installed solar capacity of the plant",
  "installed_solar_capacity_unit": "MW",
  "installed_wind_capacity": "installed wind capacity of the plant",
  "installed_wind_capacity_unit": "MW",
  "latitude": "Latitude coordinates in decimal degrees (e.g., 14.3456 or use Google Maps to find coordinates if address is known)",
  "longitude": "Longitude coordinates in decimal degrees (e.g., 79.7890 or use Google Maps to find coordinates if address is known)",
  "mandatory_closure": "",
  "name": "name of the power plant",
  "plant_address": "District or city, State, Country",
  "plant_id": "sequential number starting from 1",
  "plant_images": [],
  "plant_lifetime": 25,
  "plant_type": "The technology or fuel type of the plant",
  "potential_reference": {{
    "lat": null,
    "long": null
  }},
  "ppa_details": [
    {{
      "capacity": "The capacity covered by this PPA",
      "capacity_unit": "The unit of that capacity",
      "end_date": "The PPA's termination date (YYYY-MM-DD)",
      "respondents": [
        {{
          "capacity": "The capacity volume contracted by this respondent",
          "currency": "The currency in which the price is denominated",
          "name": "The counterparty's name",
          "price": "The contracted price per unit of energy",
          "price_unit": "The basis for the price"
        }}
      ],
      "start_date": "The PPA's commencement date (YYYY-MM-DD)",
      "tenure": "The numeric duration of the PPA",
      "tenure_type": "The unit for the tenure"
    }}
  ],
  "remaining_useful_life": "The end-of-life date (yyyy-mm-ddThh:mm:ss.000Z)",
  "total_installed_capacity": "Total installed capacity of the plant in MW",
  "total_installed_capacity_unit": "MW",
  "units_id": [
    "List of units which are operational at this plant"
  ]
}}

EXTRACTION VALIDATION REQUIREMENTS:
- Format response as either a single JSON object or array of JSON objects
- If organization has multiple plants, create separate JSON objects with sequential plant_id (1, 2, 3, etc.)
- Do not include explanatory text before or after the JSON
- If information is not available or uncertain, use empty strings, empty arrays, or appropriate defaults
- Ensure JSON is properly formatted and valid
- VALIDATE each field before including it:

FIELD-SPECIFIC VALIDATION:
- name: Must exactly match the plant name in research topic
- plant_type: Must be one of: solar, wind, thermal, biomass, hydro, nuclear, hybrid
- plant_address: Must be complete with District/City, State, Country
- latitude/longitude: Must be numeric values (e.g., 14.3456, 79.7890)
- total_installed_capacity: Must be realistic numeric MW value for the plant type
- commencement_date: Must be in yyyy-mm-ddThh:mm:ss.000Z format
- plant_lifetime: Must be realistic (typically 25-30 years)
- units_id: Must be actual unit names/identifiers (e.g., ["Unit-1", "Unit-2"])
- auxiliary_power_consumed: Must be numeric percentage values with year (e.g., {{"value": 5.2, "year": 2023}})
- cuf: Must be realistic numeric percentage values for plant type (solar: 15-25%, wind: 20-35%, thermal: 60-85%)
- gross_power_generation: Must be realistic numeric MWh/GWh values
- installed_bess_capacity: Must be numeric value
- installed_biomass_capacity: Must be numeric value
- installed_solar_capacity: Must be numeric value
- installed_wind_capacity: Must be numeric value
- ppa_details: Must include ALL nested fields with EXACT contract details:
  * capacity: Numeric value
  * capacity_unit, start_date, end_date, tenure_type: String values
  * tenure: Numeric value
  * For each respondent: 
    - name, currency, price_unit: String values
    - capacity: Numeric value
    - price: Numeric value
  * ONLY include if you have complete and accurate PPA information
- grid_connectivity_maps: Must include complete nested structure:
  * substation_name, substation_type: String values
  * capacity, latitude, longitude: Numeric values
  * For projects array: distance from substation as numeric value
  * ONLY include if you have specific grid connection details

NUMERIC FIELD REQUIREMENTS:
- The following fields MUST be numeric (not strings):
  * auxiliary_power_consumed: Numeric value with year (e.g., {{"value": 5.2, "year": 2023}})
  * cuf: Numeric value with year (e.g., {{"value": 22.5, "year": 2023}})
  * gross_power_generation: Numeric value with year (e.g., {{"value": 450000, "year": 2023}})
  * installed_bess_capacity: Numeric value (e.g., 100.5)
  * installed_biomass_capacity: Numeric value (e.g., 50.3)
  * installed_solar_capacity: Numeric value (e.g., 200.75)
  * installed_wind_capacity: Numeric value (e.g., 150.25)
  * latitude: Numeric value (e.g., 23.4567)
  * longitude: Numeric value (e.g., 77.8901)
  * total_installed_capacity: Numeric value (e.g., 500.5)
  * For ppa_details:
    - capacity: Numeric value (e.g., 100.5)
    - tenure: Numeric value (e.g., 25)
    - For each respondent:
      - capacity: Numeric value (e.g., 100.5)
      - price: Numeric value (e.g., 250.75)

- DO NOT use string quotes around numeric values
- Preserve decimal points if they exist

IMPORTANT CAPACITY FIELD LOGIC:
- Set capacity fields based on plant_type:
  * If plant_type is "solar": Set installed_solar_capacity to actual value, others to ""
  * If plant_type is "wind": Set installed_wind_capacity to actual value, others to ""
  * If plant_type is "biomass": Set installed_biomass_capacity to actual value, others to ""
  * If plant_type is "thermal": Set all renewable capacity fields to ""
  * If plant_type is "hybrid": Set multiple capacity fields as appropriate

FALLBACK CALCULATIONS FOR MISSING DATA:
- For auxiliary_power_consumed: If not found in web search, calculate for last 5 years (2020-2024) using industry standards
- For cuf: If not found in web search, calculate using formula: (Annual electricity generated)/(plant capacity*8760) for last 5 years
- Provide data for last 5 years (2020, 2021, 2022, 2023, 2024) with estimated values if actual data not available

NUMERIC VALUE REQUIREMENT:
- All numeric values for capacity, generation, CUF, etc. MUST be numeric types (not strings)
- Preserve decimal points if they exist
- DO NOT use string quotes around numeric values

⚠️ CRITICAL ACCURACY WARNING:
- This JSON will be used for important business decisions
- ONLY include data that you are confident is accurate for the specific plant: "{research_topic}"
- DO NOT guess or approximate critical values like capacity, dates, or performance metrics
- If you cannot verify a data point from the summaries, leave it as empty string/array
- DOUBLE-CHECK that all data belongs to the correct plant and location
- PRIORITIZE accuracy over completeness - empty fields are better than wrong data

Research Topic: {research_topic}

Summaries:
{summaries}
"""
        try:
            # Use a more robust approach to avoid structured output issues
            from langchain_core.messages import HumanMessage
            
            # Convert the prompt to a message
            message = HumanMessage(content=plant_prompt)
            
            # Use invoke with explicit message format
            result = llm.invoke([message])
            final_content = result.content
            
            # Process plant data with multiple plant support
            import json
            
            plant_name_for_s3 = state.get("plant_name_for_s3", research_topic)
            
            # Try to extract JSON from response
            plants_data = None
            json_str = ""
            
            def extract_json_safely(content):
                """Extract JSON from content using multiple strategies."""
                import re
                
                # Strategy 1: Look for array format first
                array_pattern = r'\[[\s\S]*\]'
                array_matches = re.findall(array_pattern, content)
                
                for match in array_matches:
                    try:
                        data = json.loads(match)
                        if isinstance(data, list):
                            return data, match
                        else:
                            return [data], match
                    except json.JSONDecodeError:
                        continue
                
                # Strategy 2: Look for object format
                obj_pattern = r'\{[\s\S]*\}'
                obj_matches = re.findall(obj_pattern, content)
                
                for match in obj_matches:
                    try:
                        data = json.loads(match)
                        return [data], match
                    except json.JSONDecodeError:
                        continue
                
                # Strategy 3: Clean and try parsing the whole content
                try:
                    # Remove common prefixes/suffixes
                    cleaned = content.strip()
                    if cleaned.startswith('```json'):
                        cleaned = cleaned[7:]
                    if cleaned.endswith('```'):
                        cleaned = cleaned[:-3]
                    
                    # Try parsing cleaned content
                    data = json.loads(cleaned.strip())
                    if isinstance(data, list):
                        return data, cleaned
                    else:
                        return [data], cleaned
                except json.JSONDecodeError:
                    pass
                
                return None, ""
            
            plants_data, json_str = extract_json_safely(final_content)
            
            print(f"[Session {session_id}] 🔍 JSON extraction debug:")
            print(f"[Session {session_id}] Content length: {len(final_content)}")
            print(f"[Session {session_id}] First 200 chars: {final_content[:200]}")
            print(f"[Session {session_id}] JSON string found: {bool(json_str)}")
            print(f"[Session {session_id}] Plants data found: {bool(plants_data)}")
            if plants_data:
                print(f"[Session {session_id}] Number of plants: {len(plants_data)}")
            
            if plants_data and json_str:
                # Process each plant and assign sequential plant_id
                processed_plants = []
                image_urls = state.get("s3_image_urls", [])
                
                for idx, plant_data in enumerate(plants_data):
                    # Assign sequential plant_id starting from 1
                    plant_id = idx + 1
                    plant_data["plant_id"] = plant_id
                    
                    # Generate sk field according to template format
                    plant_type = plant_data.get("plant_type", "Unknown")
                    sk_value = f"plant#{plant_type}#{plant_id}"
                    plant_data["sk"] = sk_value
                    
                    # Add image URLs to each plant
                    if image_urls:
                        plant_data["plant_images"] = image_urls
                        print(f"✅ Added {len(image_urls)} image URLs to plant {plant_id}")
                    
                    # Ensure all required template fields exist with defaults
                    template_defaults = {
                        "pk": "default null",
                        "annual_operational_hours": 8760,
                        "auxiliary_power_consumed": [],
                        "closure_year": "",
                        "commencement_date": "",
                        "cuf": [],
                        "gross_power_generation": [],
                        "grid_connectivity_maps": [],
                        "installed_bess_capacity": "",
                        "installed_bess_capacity_unit": "MWh",
                        "installed_biomass_capacity": "",
                        "installed_biomass_capacity_unit": "MW",
                        "installed_solar_capacity": "",
                        "installed_solar_capacity_unit": "MW",
                        "installed_wind_capacity": "",
                        "installed_wind_capacity_unit": "MW",
                        "latitude": "",
                        "longitude": "",
                        "mandatory_closure": "",
                        "plant_images": [],
                        "plant_lifetime": 25,
                        "potential_reference": {"lat": None, "long": None},
                        "ppa_details": [],
                        "remaining_useful_life": "",
                        "total_installed_capacity": "",
                        "total_installed_capacity_unit": "MW",
                        "units_id": []
                    }
                    
                    # Apply defaults for missing fields
                    for key, default_value in template_defaults.items():
                        if key not in plant_data:
                            plant_data[key] = default_value
                    
                    processed_plants.append(plant_data)
                    print(f"✅ Processed plant {plant_id}: {plant_data.get('name', 'Unknown')}")
                
                # Update final_content with processed plants
                if len(processed_plants) == 1:
                    updated_json = json.dumps(processed_plants[0], indent=2)
                else:
                    updated_json = json.dumps(processed_plants, indent=2)
                
                final_content = f"PLANT-LEVEL INFORMATION:\n{updated_json}"
                print(f"Successfully generated plant-level information for {len(processed_plants)} plant(s)")
                
                # Store each plant as separate JSON file
                current_s3_urls = state.get("s3_json_urls", {})
                current_storage_status = state.get("json_storage_complete", {})
                current_errors = state.get("json_storage_errors", [])
                
                for plant_data in processed_plants:
                    plant_id = plant_data["plant_id"]
                    plant_name = plant_data.get("name", f"Plant_{plant_id}")
                    
                    # Apply simple plant-level fallback calculations for missing data
                    try:
                        print(f"[Session {session_id}] 🔧 Applying plant-level fallback calculations for Plant {plant_id}")
                       
                        plant_capacity = extract_numeric_value(plant_data.get("total_installed_capacity", "0"))
                        plant_type = plant_data.get("plant_type", "thermal").lower()
                        
                        # Add fallback auxiliary power consumption if missing
                        if not plant_data.get("auxiliary_power_consumed"):
                            aux_percentage = get_auxiliary_power_percentage(plant_type)
                            # Keep as float
                            plant_data["auxiliary_power_consumed"] = [
                                {"value": aux_percentage, "year": year} 
                                for year in [2020, 2021, 2022, 2023, 2024]
                             ]
                            print(f"[Session {session_id}] ✅ Added fallback auxiliary power data: {aux_percentage}%")
                        
                        # Calculate CUF if missing but gross_power_generation is available
                        if not plant_data.get("cuf") and plant_data.get("gross_power_generation") and plant_capacity > 0:
                            cuf_data = []
                            for gen_record in plant_data["gross_power_generation"]:
                                if isinstance(gen_record, dict) and "value" in gen_record:
                                    # Extract MWh from generation value
                                    gen_mwh = extract_numeric_value(gen_record["value"])
                                    year = gen_record.get("year", "Unknown")
                                    
                                    if gen_mwh > 0:
                                        # CUF = (Annual Generation MWh) / (Capacity MW * 8760 hours) * 100
                                        cuf = (gen_mwh / (plant_capacity * 8760)) * 100
                                        # Keep as float
                                        cuf_data.append({
                                            "value": cuf,
                                            "year": year
                                        })
                            
                            if cuf_data:
                                plant_data["cuf"] = cuf_data
                                print(f"[Session {session_id}] ✅ Calculated CUF from generation data")
                        
                    except Exception as e:
                        print(f"[Session {session_id}] ⚠️ Plant fallback calculations failed for Plant {plant_id}: {str(e)}")
                    
                    # Filter plant data to match template - remove extra fields
                    plant_data = filter_plant_data_to_template(plant_data, session_id)
                    
                    # Ensure numeric values for specific fields
                    plant_data = ensure_numeric_values(plant_data, session_id)
                    
                    # Store each plant as separate file
                    plant_s3_url = store_plant_data(plant_data, f"{plant_name_for_s3}_Plant_{plant_id}", session_id)
                    
                    if plant_s3_url:
                        print(f"[Session {session_id}] ✅ Plant {plant_id} JSON saved to S3: {plant_s3_url}")
                        if "plants" not in current_s3_urls:
                            current_s3_urls["plants"] = {}
                        current_s3_urls["plants"][str(plant_id)] = plant_s3_url
                        
                        if "plants" not in current_storage_status:
                            current_storage_status["plants"] = {}
                        current_storage_status["plants"][str(plant_id)] = True
                    else:
                        print(f"[Session {session_id}] ❌ Failed to save Plant {plant_id} JSON to S3")
                        current_errors.append(f"Failed to save Plant {plant_id} JSON to S3")
            else:
                raise Exception("Could not extract valid JSON from plant response")
            
        except Exception as e:
            import traceback
            print(f"Error in plant-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback plant-level response
            plant_name = research_topic.strip()
            final_content = f"""PLANT-LEVEL INFORMATION:
{{
  "sk": "plant#Unknown#1",
  "pk": "default null",
  "annual_operational_hours": 8760,
  "auxiliary_power_consumed": [],
  "closure_year": "",
  "commencement_date": "",
  "cuf": [],
  "gross_power_generation": [],
  "grid_connectivity_maps": [],
  "installed_bess_capacity": "",
  "installed_bess_capacity_unit": "MWh",
  "installed_biomass_capacity": "",
  "installed_biomass_capacity_unit": "MW",
  "installed_solar_capacity": "",
  "installed_solar_capacity_unit": "MW",
  "installed_wind_capacity": "",
  "installed_wind_capacity_unit": "MW",
  "latitude": "",
  "longitude": "",
  "mandatory_closure": "",
  "name": "{plant_name}",
  "plant_address": "",
  "plant_id": 1,
  "plant_images": [],
  "plant_lifetime": 25,
  "plant_type": "Unknown",
  "potential_reference": {{"lat": null, "long": null}},
  "ppa_details": [],
  "remaining_useful_life": "",
  "total_installed_capacity": "",
  "total_installed_capacity_unit": "MW",
  "units_id": []
}}"""
            
            current_s3_urls = state.get("s3_json_urls", {})
            current_storage_status = state.get("json_storage_complete", {})
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"Plant-level extraction failed: {str(e)}")
            
            print("Using fallback plant-level information")

        # Filter sources that were actually used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)

        # Return plant-level results with S3 updates
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
        }
    
    else:
        # No other phases needed - only organization and plant level
        return {
            "messages": [AIMessage(content="Processing complete. Organization and plant level data extracted.")],
            "sources_gathered": state.get("sources_gathered", []),
            "s3_json_urls": state.get("s3_json_urls", {}),
            "json_storage_complete": state.get("json_storage_complete", {}),
            "json_storage_errors": state.get("json_storage_errors", []),
        }


# Define a clear state transition node between organization and plant level
def clear_state_for_plant_level(state: OverallState) -> OverallState:
    """Clear the state for plant-level research after organization-level is complete."""
    session_id = state.get("session_id", "unknown")
    research_topic = state.get("research_topic", "")
    
    print(f"[Session {session_id}] 🔄 CLEARING STATE FOR PLANT LEVEL")
    print(f"[Session {session_id}] Organization phase complete, starting plant phase")
    print(f"[Session {session_id}] Validating research topic for plant phase: '{research_topic}'")
    
    # Validate that we have a valid research topic before proceeding to plant phase
    if not research_topic or not research_topic.strip():
        print(f"[Session {session_id}] ❌ ERROR: Cannot proceed to plant phase without research topic!")
        print(f"[Session {session_id}] Terminating workflow due to missing research topic")
        
        # Return terminal state instead of proceeding
        return {
            "messages": state.get("messages", []) + [AIMessage(content="ERROR: Cannot proceed with research - no topic provided")],
            "session_id": session_id,
            "search_phase": 999,  # Invalid phase to stop workflow
            "phase_complete": True,
            "research_complete": True,
            "error": "No research topic provided"
        }
    
    # Preserve essential data and clear research-specific data for new phase
    return {
        "messages": state.get("messages", []),
        "session_id": session_id,
        "research_topic": research_topic,  # Preserve research topic
        "search_phase": 2,  # Switch to plant-level
        "research_loop_count": 0,  # Reset for plant phase
        "web_research_result": [],  # Clear for new phase
        "search_query": [],  # Clear for new phase
        "sources_gathered": [],  # Clear for new phase
        "continue_research": False,  # Reset
        "phase_complete": False,  # Reset
        "initial_search_query_count": state.get("initial_search_query_count", 5),
        "max_research_loops": state.get("max_research_loops", 3),
        "reasoning_model": state.get("reasoning_model", ""),
        # Preserve S3 and session data
        "plant_name_for_s3": state.get("plant_name_for_s3", ""),
        "s3_json_urls": state.get("s3_json_urls", {}),
        "json_storage_complete": state.get("json_storage_complete", {}),
        "json_storage_errors": state.get("json_storage_errors", []),
        # Preserve image extraction results
        "image_extraction_complete": state.get("image_extraction_complete", False),
        "s3_image_urls": state.get("s3_image_urls", []),
        "image_extraction_error": state.get("image_extraction_error", ""),
    }


# Create our Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Add session initialization node
builder.add_node("initialize_session", initialize_session)

# Add parallel image extraction node
builder.add_node("extract_images_parallel", extract_images_parallel)

# Define organization-level research nodes
builder.add_node("org_generate_query", lambda state, config: generate_query({**state, "search_phase": 1}, config))
builder.add_node("org_web_research", web_research)
builder.add_node("org_reflection", reflection)
builder.add_node("org_finalize_answer", lambda state, config: finalize_answer_parallel({**state, "search_phase": 1}, config))

# Add state transition node
builder.add_node("clear_state_for_plant_level", clear_state_for_plant_level)

# Define plant-level research nodes  
builder.add_node("plant_generate_query", lambda state, config: generate_query({**state, "search_phase": 2}, config))
builder.add_node("plant_web_research", web_research)
builder.add_node("plant_reflection", reflection)

def plant_finalize_answer_debug(state, config):
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_finalize_answer called")
    result = finalize_answer_parallel({**state, "search_phase": 2}, config)
    print(f"[Session {session_id}] ✅ plant_finalize_answer completed")
    return result

builder.add_node("plant_finalize_answer", plant_finalize_answer_debug)

# Add the graph edges for two-phase processing
# Start with session initialization
builder.add_edge(START, "initialize_session")

# Start parallel image extraction alongside organization research
builder.add_edge("initialize_session", "extract_images_parallel")
builder.add_edge("initialize_session", "org_generate_query")

# Organization-level flow
builder.add_edge("org_generate_query", "org_web_research")
builder.add_edge("org_web_research", "org_reflection")

def should_continue_org_research(state):
    continue_research = state.get("continue_research", False)
    research_loop_count = state.get("research_loop_count", 0)
    max_research_loops = state.get("max_research_loops", 3)
    
    if continue_research and research_loop_count < max_research_loops:
        return "org_generate_query"
    else:
        return "org_finalize_answer"

builder.add_conditional_edges("org_reflection", should_continue_org_research)
builder.add_edge("org_finalize_answer", "clear_state_for_plant_level")

# Plant-level flow
builder.add_edge("clear_state_for_plant_level", "plant_generate_query") 
builder.add_edge("plant_generate_query", "plant_web_research")
builder.add_edge("plant_web_research", "plant_reflection")

def should_continue_plant_research(state):
    continue_research = state.get("continue_research", False)
    research_loop_count = state.get("research_loop_count", 0)
    max_research_loops = state.get("max_research_loops", 3)
    
    if continue_research and research_loop_count < max_research_loops:
        return "plant_generate_query"
    else:
        return "plant_finalize_answer"

builder.add_conditional_edges("plant_reflection", should_continue_plant_research)
builder.add_edge("plant_finalize_answer", END)

# Create the compiled graph
graph = builder.compile()