"""
Parallel extraction system for images and JSON processing.
This module handles concurrent image extraction and JSON data processing
to improve overall system performance.
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from agent.image_extraction import extract_and_upload_images
from agent.json_s3_storage import (
    store_organization_data,
    store_plant_data,
    store_unit_data,
    sanitize_plant_name
)


@dataclass
class ExtractionResult:
    """Result container for parallel extraction operations."""
    success: bool
    data: Any
    error_message: str
    duration: float
    operation_type: str


class ParallelExtractionManager:
    """Manages parallel extraction of images and JSON data."""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def extract_images_async(self, research_topic: str, session_id: str) -> ExtractionResult:
        """Asynchronously extract and upload images."""
        start_time = time.time()
        
        try:
            print(f"[Session {session_id}] 🖼️ Starting async image extraction for: {research_topic}")
            
            # Run image extraction in thread pool
            loop = asyncio.get_event_loop()
            s3_urls = await loop.run_in_executor(
                self.executor, 
                extract_and_upload_images, 
                research_topic, 
                session_id
            )
            
            duration = time.time() - start_time
            
            if s3_urls:
                print(f"[Session {session_id}] ✅ Async image extraction successful: {len(s3_urls)} images in {duration:.2f}s")
                return ExtractionResult(
                    success=True,
                    data=s3_urls,
                    error_message="",
                    duration=duration,
                    operation_type="image_extraction"
                )
            else:
                print(f"[Session {session_id}] ⚠️ No images found in {duration:.2f}s")
                return ExtractionResult(
                    success=True,
                    data=[],
                    error_message="No images found",
                    duration=duration,
                    operation_type="image_extraction"
                )
                
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"Image extraction failed: {str(e)}"
            print(f"[Session {session_id}] ❌ {error_msg} in {duration:.2f}s")
            
            return ExtractionResult(
                success=False,
                data=[],
                error_message=error_msg,
                duration=duration,
                operation_type="image_extraction"
            )
    
    async def store_json_async(self, data: Dict, data_type: str, name: str, session_id: str) -> ExtractionResult:
        """Asynchronously store JSON data to S3."""
        start_time = time.time()
        
        try:
            print(f"[Session {session_id}] 💾 Starting async JSON storage for {data_type}: {name}")
            
            # Choose appropriate storage function based on data type
            if data_type == "organization":
                storage_func = store_organization_data
            elif data_type == "plant":
                storage_func = store_plant_data
            elif data_type == "unit":
                storage_func = store_unit_data
            else:
                raise ValueError(f"Unknown data type: {data_type}")
            
            # Run JSON storage in thread pool
            loop = asyncio.get_event_loop()
            s3_url = await loop.run_in_executor(
                self.executor,
                storage_func,
                data,
                name,
                session_id
            )
            
            duration = time.time() - start_time
            
            if s3_url:
                print(f"[Session {session_id}] ✅ Async JSON storage successful for {data_type} in {duration:.2f}s: {s3_url}")
                return ExtractionResult(
                    success=True,
                    data=s3_url,
                    error_message="",
                    duration=duration,
                    operation_type=f"json_storage_{data_type}"
                )
            else:
                error_msg = f"Failed to store {data_type} JSON"
                print(f"[Session {session_id}] ❌ {error_msg} in {duration:.2f}s")
                return ExtractionResult(
                    success=False,
                    data=None,
                    error_message=error_msg,
                    duration=duration,
                    operation_type=f"json_storage_{data_type}"
                )
                
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"JSON storage failed for {data_type}: {str(e)}"
            print(f"[Session {session_id}] ❌ {error_msg} in {duration:.2f}s")
            
            return ExtractionResult(
                success=False,
                data=None,
                error_message=error_msg,
                duration=duration,
                operation_type=f"json_storage_{data_type}"
            )
    
    async def parallel_extract_all(
        self, 
        research_topic: str, 
        json_data_list: List[Tuple[Dict, str, str]], 
        session_id: str
    ) -> Dict[str, ExtractionResult]:
        """
        Extract images and store multiple JSON data in parallel.
        
        Args:
            research_topic: Topic for image extraction
            json_data_list: List of (data, data_type, name) tuples for JSON storage
            session_id: Session identifier
            
        Returns:
            Dictionary of operation results
        """
        start_time = time.time()
        print(f"[Session {session_id}] 🚀 Starting parallel extraction for {len(json_data_list)} JSON items + images")
        
        # Create tasks for parallel execution
        tasks = []
        task_names = []
        
        # Add image extraction task
        tasks.append(self.extract_images_async(research_topic, session_id))
        task_names.append("images")
        
        # Add JSON storage tasks
        for i, (data, data_type, name) in enumerate(json_data_list):
            tasks.append(self.store_json_async(data, data_type, name, session_id))
            task_names.append(f"{data_type}_{i}")
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        operation_results = {}
        for i, (result, task_name) in enumerate(zip(results, task_names)):
            if isinstance(result, Exception):
                operation_results[task_name] = ExtractionResult(
                    success=False,
                    data=None,
                    error_message=str(result),
                    duration=0,
                    operation_type=f"parallel_{task_name}"
                )
            else:
                operation_results[task_name] = result
        
        total_duration = time.time() - start_time
        successful_ops = sum(1 for r in operation_results.values() if r.success)
        
        print(f"[Session {session_id}] 📊 Parallel extraction completed: {successful_ops}/{len(tasks)} successful in {total_duration:.2f}s")
        
        return operation_results
    
    def close(self):
        """Clean up executor resources."""
        self.executor.shutdown(wait=True)


# Global extraction manager instance
extraction_manager = ParallelExtractionManager()


async def enhanced_parallel_extraction(research_topic: str, org_data: Dict, plant_data_list: List[Dict], session_id: str) -> Dict:
    """
    Enhanced parallel extraction that processes images and all JSON data simultaneously.
    
    Args:
        research_topic: Research topic for image extraction
        org_data: Organization data to store
        plant_data_list: List of plant data dictionaries to store
        session_id: Session identifier
        
    Returns:
        Combined results dictionary
    """
    # Prepare JSON data list
    json_data_list = []
    
    # Add organization data
    if org_data:
        org_name = sanitize_plant_name(research_topic)
        json_data_list.append((org_data, "organization", f"{org_name}_organization", session_id))
    
    # Add plant data
    for i, plant_data in enumerate(plant_data_list):
        plant_name = plant_data.get("name", f"Plant_{i}")
        plant_id = plant_data.get("plant_id", i)
        sanitized_name = sanitize_plant_name(plant_name)
        json_data_list.append((plant_data, "plant", f"{sanitized_name}_Plant_{plant_id}", session_id))
    
    # Execute parallel extraction
    results = await extraction_manager.parallel_extract_all(research_topic, json_data_list, session_id)
    
    # Process results into expected format
    processed_results = {
        "image_extraction_complete": False,
        "s3_image_urls": [],
        "image_extraction_error": "",
        "json_storage_complete": {},
        "s3_json_urls": {},
        "json_storage_errors": [],
        "parallel_extraction_stats": {
            "total_operations": len(results),
            "successful_operations": sum(1 for r in results.values() if r.success),
            "total_duration": max(r.duration for r in results.values()) if results else 0
        }
    }
    
    # Process image results
    if "images" in results:
        image_result = results["images"]
        processed_results["image_extraction_complete"] = True
        if image_result.success:
            processed_results["s3_image_urls"] = image_result.data
        else:
            processed_results["image_extraction_error"] = image_result.error_message
    
    # Process JSON results
    for task_name, result in results.items():
        if task_name != "images" and result.operation_type.startswith("json_storage"):
            data_type = result.operation_type.replace("json_storage_", "")
            
            if data_type not in processed_results["json_storage_complete"]:
                processed_results["json_storage_complete"][data_type] = {}
                processed_results["s3_json_urls"][data_type] = {}
            
            if result.success:
                # Extract index from task name if it exists
                if "_" in task_name:
                    index = task_name.split("_")[-1]
                    processed_results["json_storage_complete"][data_type][index] = True
                    processed_results["s3_json_urls"][data_type][index] = result.data
                else:
                    processed_results["json_storage_complete"][data_type]["0"] = True
                    processed_results["s3_json_urls"][data_type]["0"] = result.data
            else:
                processed_results["json_storage_errors"].append(result.error_message)
    
    return processed_results


def parallel_extraction_sync(research_topic: str, org_data: Dict, plant_data_list: List[Dict], session_id: str) -> Dict:
    """
    Synchronous wrapper for enhanced parallel extraction.
    This can be called from LangGraph nodes.
    """
    # Run the async function
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(
            enhanced_parallel_extraction(research_topic, org_data, plant_data_list, session_id)
        )
        return result
    finally:
        loop.close()